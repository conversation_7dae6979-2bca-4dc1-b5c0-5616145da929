#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整双向用例对比工具 - 简化运行脚本
同时进行正向和反向对比分析
"""

import os
import sys
from case_comparison_tool import CaseComparisonTool


def main():
    """主函数 - 双向对比版本"""
    print("🔄 完整双向用例对比分析")
    print("=" * 50)
    print("功能: 同时进行正向和反向对比")
    print("正向: 找出未完成的用例")
    print("反向: 找出多余的用例")
    print("输入: case_total.xlsx + case_done.xlsx")
    print("输出: missing_cases_[时间戳].xlsx + extra_cases_[时间戳].xlsx")
    print("=" * 50)
    
    # 创建工具实例
    tool = CaseComparisonTool()
    
    # 检查输入文件是否存在
    if not os.path.exists(tool.case_total_file):
        print(f"❌ 全量用例文件不存在: {tool.case_total_file}")
        return False
    
    if not os.path.exists(tool.case_done_file):
        print(f"❌ 已完成用例文件不存在: {tool.case_done_file}")
        return False
    
    print("✅ 输入文件检查通过")
    
    # 执行双向对比分析
    missing_path, extra_path = tool.run_full_comparison()
    
    success = False
    if missing_path or extra_path:
        print(f"\n🎉 双向对比分析完成！")
        
        if missing_path:
            print(f"📁 未完成用例文件: {os.path.basename(missing_path)}")
            success = True
        
        if extra_path:
            print(f"📁 多余用例文件: {os.path.basename(extra_path)}")
            success = True
        
        print(f"\n💡 提示: 每个Excel文件都包含多个工作表:")
        print(f"   📋 主数据表 - 详细的用例列表")
        print(f"   📊 统计信息 - 整体统计")
        print(f"   🏢 模块统计 - 按模块分组的统计信息")
        
        return success
    else:
        print("\n❌ 分析失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
