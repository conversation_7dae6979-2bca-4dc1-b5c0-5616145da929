from datetime import datetime, timedelta
import json
import time

import requests
from task_manager.backend.src.tools.feishu_tools import FeishuBot
from task_manager.backend.src.tools.robot_tool import Robot


class IpmTools:
    def __init__(self):
        # self.headers = self.get_token()
        self.feishu_bot = FeishuBot()
        # self.robot = Robot(webhook_url='https://open.feishu.cn/open-apis/bot/v2/hook/a2fe953f-21d3-41b7-a8e5-cf40dcca012b') # 测开小群机器人
        self.robot = Robot(
            webhook_url='https://open.feishu.cn/open-apis/bot/v2/hook/b905c3fe-4cae-408b-afad-a78e1e807eee')  # 测开小群机器人

    def get_token(self):
        url = "https://pfgateway.transsion.com:9199/uac-auth-service/v2/api/uac-auth/rtoken/get"

        payload = json.dumps({
            "utoken": "u_OTM3MWl1MDduOWdqdTN4dnFnNmxeOTA4MTkzNDkwNDEyMjk1OTIxMjE3MjA1"
        })
        headers = {
            'Content-Type': 'application/json',
            # 'Cookie': 'acw_tc=0a03846d17491919192118777e4f39e70605c924263c12224a29f2b2ddc11f'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        # print(response.text)
        token = response.json()['data']
        # 对字段进行字段转化
        tmp = {
            'P-Auth': token['rtoken'],
            'P-Rtoken': token['utoken'],
            'Content-Type': 'application/json',
        }
        return tmp

    def queryNoneSubmissionInfo(self, beginDate='2025-06-05', endDate='2025-06-06', deptIdList='704463829945810949'):
        import requests
        import json

        url = "https://pfgatewaysz.transsion.com:9199/service-ipm-tsm/tsm/noneSubmission/queryNoneSubmissionInfo"

        payload = json.dumps({
            "count": True,
            "param": {
                "beginDate": beginDate,
                "endDate": endDate,
                "deptIdList": [
                    deptIdList
                ]
            },
            "current": 1,
            "size": 100
        })
        # headers = {
        #     'P-Auth': 'r_OTQ3MWVyOHNuZXN6cjZhNjU1azVeOTA4MTkzNDkwMTk0MTM3OTIxMjE3MjA1',
        #     'P-Rtoken': 'u_OTM3MWl1MDduOWdqdTN4dnFnNmxeOTA4MTkzNDkwNDEyMjk1OTIxMjE3MjA1',
        #     'Content-Type': 'application/json',
        #     # 'Cookie': 'acw_tc=0a0011c017491936384277422e3e75c8475b6ef95d48b360cf4d15ef6923d3'
        # }
        headers = self.get_token()

        response = requests.request("POST", url, headers=headers, data=payload)

        # print(response.text)
        data = response.json().get('data', None)
        if data:
            return data
        else:
            raise Exception('查询无结果')

    def get_datatime(self):
        # 获取当前日期
        current_date = datetime.now()

        # 计算往前 7 天的日期
        seven_days_ago = current_date - timedelta(days=7)

        # 格式化为 "2025-06-06" 格式
        current_date_str = current_date.strftime("%Y-%m-%d")
        seven_days_ago_str = seven_days_ago.strftime("%Y-%m-%d")

        # print("当前日期:", current_date_str)
        # print("往前 7 天的日期:", seven_days_ago_str)

        return {
            'current_date_str': current_date_str,
            'seven_days_ago_str': seven_days_ago_str
        }

    def do_notify_none_submission(self):

        beginDate = self.get_datatime()['seven_days_ago_str']
        endDate = self.get_datatime()['current_date_str']

        data = self.queryNoneSubmissionInfo(beginDate, endDate)
        if len(data['data']) == 0:  # 没有数据,无需通知
            return
        for item in data['data']:
            assignee = item['workerName']
            noneSubmissionDays = item['noneSubmissionDays']
            # dateList = item['dateList']

            if int(noneSubmissionDays) <= 1:  # 未提报天数未超过1天,无需通知
                continue

            tmp = '{"text":"请及时处理未提报工时，（不含当天），当前已有' + str(
                noneSubmissionDays - 1) + '天未提报工时 跳转链接' + 'https://ipm.transsion.com/#/app-center/tsm/submission' + '"}'

            print(tmp)
            print(assignee)
            # # if assignee=='卜昌义':
            # #     self.feishu_bot.send_bug_notification_v5(assignee, tmp)
            # #     break
            # self.feishu_bot.send_bug_notification_v5(assignee, tmp)
            # time.sleep(1)

    def do_notify_none_submission_by_robot_at_18(self):
        """

        :return:
        """

        def process_string(s):
            """
            处理字符串:
            - 如果长度为3，不处理
            - 如果长度小于3，用空格补全到3个字符
            - 如果长度大于3，不处理

            参数:
                s (str): 输入的字符串

            返回:
                str: 处理后的字符串
            """
            if len(s) == 3:
                return s
            elif len(s) < 3:
                # 用空格补全到3个字符
                return s + ' ' * (3 - len(s))
            else:  # len(s) > 3
                return s

        beginDate = self.get_datatime()['seven_days_ago_str']
        endDate = self.get_datatime()['current_date_str']

        data = self.queryNoneSubmissionInfo(beginDate, endDate, deptIdList="704463829941616642")  # 软件产品提示
        data = self.preprocess_data_by_dept_third(data)
        print(json.dumps(data, ensure_ascii=False, indent=2))

        # if len(data['data']) == 0:  # 没有数据,无需通知
        #     return
        # text = ''
        # for item in data['data']:
        #     assignee = item['workerName']
        #     assignee = process_string(assignee)
        #     noneSubmissionDays = item['noneSubmissionDays']
        #     # dateList = item['dateList']

            # 当天18:00提醒，需要包含当天数据
            # if int(noneSubmissionDays) <= 1:  # 未提报天数未超过1天,无需通知
            #     continue
            # text += assignee + ' ' + '当前已有' + str(noneSubmissionDays) + '天未提报工时' + '\n'

        # self.robot.send_card_message(
        #     title="软件产品部门IPM工时未填写提醒(含当天)",
        #     content=text,
        #     buttons=[
        #         {"text": "IPM工时填写入口", "url": "https://ipm.transsion.com/#/app-center/tsm/submission"}
        #     ],
        #     template="red",at_all=True
        # )

    def do_notify_none_submission_by_robot_at_8_59(self):
        """

        :return:
        """

        def process_string(s):
            """
            处理字符串:
            - 如果长度为3，不处理
            - 如果长度小于3，用空格补全到3个字符
            - 如果长度大于3，不处理

            参数:
                s (str): 输入的字符串

            返回:
                str: 处理后的字符串
            """
            if len(s) == 3:
                return s
            elif len(s) < 3:
                # 用空格补全到3个字符
                return s + ' ' * (3 - len(s))
            else:  # len(s) > 3
                return s

        beginDate = self.get_datatime()['seven_days_ago_str']
        endDate = self.get_datatime()['current_date_str']

        data = self.queryNoneSubmissionInfo(beginDate, endDate, deptIdList="704463829941616642")  # 软件产品提示
        if len(data['data']) == 0:  # 没有数据,无需通知
            return
        text = ''
        for item in data['data']:
            assignee = item['workerName']
            assignee = process_string(assignee)
            noneSubmissionDays = item['noneSubmissionDays']
            # dateList = item['dateList']

            # 当天8:59提醒，需要包含当天数据
            if int(noneSubmissionDays) <= 1:  # 未提报天数未超过1天,无需通知
                continue
            text += assignee + ' ' + '当前已有' + str(noneSubmissionDays - 1) + '天未提报工时' + '\n'

        self.robot.send_card_message(
            title="软件产品部门IPM工时未填写提醒(不含当天)",
            content=text,
            buttons=[
                {"text": "IPM工时填写入口", "url": "https://ipm.transsion.com/#/app-center/tsm/submission"}
            ],
            template="red", at_all=True
        )

    def preprocess_data_by_dept_third(self, data):
        """
        按照三级部门分组数据进行预处理

        参数:
            data (dict): 原始数据，包含data字段的字典

        返回:
            dict: 按三级部门分组的数据，deptNameThird为空的放在leader中

        示例:
            {
                'RT_系统产品测试部': [
                    {'workerName': '杨将来', 'noneSubmissionDays': 2},
                    {'workerName': '卜昌义', 'noneSubmissionDays': 2}
                ],
                'RT_独立产品测试部': [
                    {'workerName': '李志祥', 'noneSubmissionDays': 2},
                    {'workerName': '卜昌义', 'noneSubmissionDays': 2}
                ],
                'leader': [
                    {'workerName': '卜昌义', 'noneSubmissionDays': 2}
                ]
            }
        """
        result = {}

        # 检查数据格式
        if not isinstance(data, dict) or 'data' not in data:
            raise ValueError("输入数据格式错误，需要包含'data'字段的字典")

        data_list = data.get('data', [])

        for item in data_list:
            # 提取需要的字段
            worker_name = item.get('workerName', '')
            none_submission_days = item.get('noneSubmissionDays', 0)
            dept_name_third = item.get('deptNameThird', '')

            # 构建输出项
            worker_info = {
                'workerName': worker_name,
                'noneSubmissionDays': none_submission_days
            }

            # 根据deptNameThird是否为空进行分组
            if dept_name_third and dept_name_third.strip():
                # deptNameThird不为空，按部门分组
                if dept_name_third not in result:
                    result[dept_name_third] = []
                result[dept_name_third].append(worker_info)
            else:
                # deptNameThird为空，放入leader组
                if 'leader' not in result:
                    result['leader'] = []
                result['leader'].append(worker_info)

        return result


if __name__ == '__main__':
    # 创建一个简单的测试，不依赖其他模块
    ipm_tools = IpmTools()
    ipm_tools.do_notify_none_submission_by_robot_at_18()
