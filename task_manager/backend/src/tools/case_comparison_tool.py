#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用例对比工具
功能：比较case_total.xlsx和case_done.xlsx，找出未完成的用例
"""

import os
import pandas as pd
from datetime import datetime
import traceback


class CaseComparisonTool:
    """用例对比工具"""
    
    def __init__(self):
        current_dir = os.path.dirname(os.path.abspath(__file__))
        src_dir = os.path.dirname(current_dir)
        self.excel_dir = os.path.join(src_dir, 'data', 'excel')
        self.case_total_file = os.path.join(self.excel_dir, 'case_total.xlsx')
        self.case_done_file = os.path.join(self.excel_dir, 'case_done.xlsx')
        
    def read_excel_file(self, file_path, description=""):
        """
        读取Excel文件
        :param file_path: 文件路径
        :param description: 文件描述
        :return: DataFrame
        """
        try:
            print(f"📖 正在读取{description}: {os.path.basename(file_path)}")
            
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return None
                
            # 读取Excel文件
            df = pd.read_excel(file_path, dtype=str)
            print(f"✅ 成功读取 {len(df)} 条记录")
            print(f"   列名: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            print(f"❌ 读取文件失败: {str(e)}")
            print(traceback.format_exc())
            return None
    
    def extract_case_ids(self, df, id_column):
        """
        提取用例ID列表
        :param df: DataFrame
        :param id_column: ID列名
        :return: 用例ID集合
        """
        try:
            if id_column not in df.columns:
                print(f"❌ 列 '{id_column}' 不存在")
                return set()
            
            # 提取非空的用例ID
            case_ids = df[id_column].dropna().astype(str).str.strip()
            case_ids = set(case_ids[case_ids != ''])
            
            print(f"📊 提取到 {len(case_ids)} 个有效用例ID")
            return case_ids
            
        except Exception as e:
            print(f"❌ 提取用例ID失败: {str(e)}")
            return set()
    
    def find_missing_cases(self, total_df, done_df):
        """
        找出未完成的用例
        :param total_df: 全量用例DataFrame
        :param done_df: 已完成用例DataFrame
        :return: 未完成用例DataFrame
        """
        try:
            print("\n🔍 开始对比用例...")
            
            # 提取用例ID
            total_ids = self.extract_case_ids(total_df, '*TCID')
            done_ids = self.extract_case_ids(done_df, '用例ID')
            
            print(f"📈 全量用例数: {len(total_ids)}")
            print(f"📋 已完成用例数: {len(done_ids)}")
            
            # 找出未完成的用例ID
            missing_ids = total_ids - done_ids
            print(f"🔍 未完成用例数: {len(missing_ids)}")
            
            if len(missing_ids) == 0:
                print("🎉 所有用例都已完成！")
                return pd.DataFrame()
            
            # 筛选出未完成的用例记录
            missing_df = total_df[total_df['*TCID'].isin(missing_ids)].copy()
            
            # 添加对比时间戳
            missing_df['对比时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            missing_df['状态'] = '未完成'
            
            print(f"✅ 成功筛选出 {len(missing_df)} 条未完成用例记录")
            
            return missing_df
            
        except Exception as e:
            print(f"❌ 对比用例失败: {str(e)}")
            print(traceback.format_exc())
            return pd.DataFrame()
    
    def save_missing_cases(self, missing_df, output_path=None):
        """
        保存未完成用例到Excel文件
        :param missing_df: 未完成用例DataFrame
        :param output_path: 输出文件路径
        :return: 保存的文件路径
        """
        try:
            if missing_df.empty:
                print("📝 没有未完成的用例，无需保存文件")
                return None
            
            # 生成输出文件路径
            if output_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_path = os.path.join(self.excel_dir, f"missing_cases_{timestamp}.xlsx")
                # output_path = f"task_manager/backend/src/data/excel/missing_cases_{timestamp}.xlsx"
            
            print(f"💾 正在保存未完成用例到: {os.path.basename(output_path)}")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 保存到Excel文件
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 主要数据
                missing_df.to_excel(writer, sheet_name='未完成用例', index=False)
                
                # 统计信息
                stats_data = {
                    '统计项': ['全量用例数', '已完成用例数', '未完成用例数', '完成率'],
                    '数值': [
                        len(missing_df) + len(self.done_ids) if hasattr(self, 'done_ids') else '未知',
                        len(self.done_ids) if hasattr(self, 'done_ids') else '未知',
                        len(missing_df),
                        f"{(len(self.done_ids) / (len(missing_df) + len(self.done_ids)) * 100):.1f}%" if hasattr(self, 'done_ids') else '未知'
                    ]
                }
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)
                
                # 按模块分组统计
                if '*Component' in missing_df.columns:
                    component_stats = missing_df['*Component'].value_counts().reset_index()
                    component_stats.columns = ['模块', '未完成用例数']
                    component_stats.to_excel(writer, sheet_name='模块统计', index=False)
            
            print(f"✅ 文件保存成功: {output_path}")
            print(f"📊 保存了 {len(missing_df)} 条未完成用例记录")
            
            return output_path
            
        except Exception as e:
            print(f"❌ 保存文件失败: {str(e)}")
            print(traceback.format_exc())
            return None
    
    def run_comparison(self, output_path=None):
        """
        执行完整的用例对比流程
        :param output_path: 输出文件路径
        :return: 输出文件路径
        """
        print("🚀 开始用例对比分析...")
        print("=" * 60)

        # 读取全量用例文件
        total_df = self.read_excel_file(self.case_total_file, "全量用例文件")
        if total_df is None:
            return None

        # 读取已完成用例文件
        done_df = self.read_excel_file(self.case_done_file, "已完成用例文件")
        if done_df is None:
            return None

        # 提取用例ID用于统计
        total_ids = self.extract_case_ids(total_df, '*TCID')
        done_ids = self.extract_case_ids(done_df, '用例ID')
        self.done_ids = done_ids  # 保存供其他方法使用

        # 找出未完成的用例
        missing_df = self.find_missing_cases(total_df, done_df)

        # 生成分析报告
        self.generate_summary_report(missing_df, len(total_ids), len(done_ids))

        # 保存结果
        result_path = self.save_missing_cases(missing_df, output_path)

        print("\n" + "=" * 60)
        print("🎯 用例对比分析完成！")

        return result_path


    def generate_summary_report(self, missing_df, total_count, done_count):
        """
        生成详细的分析报告
        :param missing_df: 未完成用例DataFrame
        :param total_count: 全量用例数
        :param done_count: 已完成用例数
        """
        print("\n📊 分析报告:")
        print("-" * 40)
        print(f"📈 全量用例总数: {total_count}")
        print(f"✅ 已完成用例数: {done_count}")
        print(f"❌ 未完成用例数: {len(missing_df)}")
        print(f"📊 完成率: {(done_count / total_count * 100):.1f}%")

        if not missing_df.empty and '*Component' in missing_df.columns:
            print(f"\n🔍 未完成用例按模块分布:")
            component_stats = missing_df['*Component'].value_counts().head(5)
            for component, count in component_stats.items():
                print(f"   {component}: {count} 个用例")

        if not missing_df.empty and '*Owner' in missing_df.columns:
            print(f"\n👥 未完成用例按负责人分布:")
            owner_stats = missing_df['*Owner'].value_counts().head(5)
            for owner, count in owner_stats.items():
                print(f"   {owner}: {count} 个用例")


def main():
    """主函数"""
    print("📋 用例对比工具")
    print("=" * 60)
    print("功能: 比较全量用例和已完成用例，生成未完成用例清单")
    print("输入: case_total.xlsx (全量用例) + case_done.xlsx (已完成用例)")
    print("输出: missing_cases_[时间戳].xlsx (未完成用例)")
    print("=" * 60)

    # 创建对比工具实例
    tool = CaseComparisonTool()

    # 执行对比分析
    result_path = tool.run_comparison()

    if result_path:
        print(f"\n🎉 分析完成！结果已保存到:")
        print(f"📁 {result_path}")
        print(f"\n💡 提示: Excel文件包含3个工作表:")
        print(f"   📋 未完成用例 - 详细的未完成用例列表")
        print(f"   📊 统计信息 - 整体完成情况统计")
        print(f"   🏢 模块统计 - 按模块分组的统计信息")
    else:
        print("\n❌ 分析失败，请检查文件路径和格式")


if __name__ == "__main__":
    main()
