#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用例对比工具
功能：比较case_total.xlsx和case_done.xlsx，找出未完成的用例
"""

import os
import pandas as pd
from datetime import datetime
import traceback


class CaseComparisonTool:
    """用例对比工具"""
    
    def __init__(self):
        self.case_total_file = "task_manager/backend/src/data/excel/case_total.xlsx"
        self.case_done_file = "task_manager/backend/src/data/excel/case_done.xlsx"
        
    def read_excel_file(self, file_path, description=""):
        """
        读取Excel文件
        :param file_path: 文件路径
        :param description: 文件描述
        :return: DataFrame
        """
        try:
            print(f"📖 正在读取{description}: {os.path.basename(file_path)}")
            
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return None
                
            # 读取Excel文件
            df = pd.read_excel(file_path, dtype=str)
            print(f"✅ 成功读取 {len(df)} 条记录")
            print(f"   列名: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            print(f"❌ 读取文件失败: {str(e)}")
            print(traceback.format_exc())
            return None
    
    def extract_case_ids(self, df, id_column):
        """
        提取用例ID列表
        :param df: DataFrame
        :param id_column: ID列名
        :return: 用例ID集合
        """
        try:
            if id_column not in df.columns:
                print(f"❌ 列 '{id_column}' 不存在")
                return set()
            
            # 提取非空的用例ID
            case_ids = df[id_column].dropna().astype(str).str.strip()
            case_ids = set(case_ids[case_ids != ''])
            
            print(f"📊 提取到 {len(case_ids)} 个有效用例ID")
            return case_ids
            
        except Exception as e:
            print(f"❌ 提取用例ID失败: {str(e)}")
            return set()
    
    def find_missing_cases(self, total_df, done_df):
        """
        找出未完成的用例
        :param total_df: 全量用例DataFrame
        :param done_df: 已完成用例DataFrame
        :return: 未完成用例DataFrame
        """
        try:
            print("\n🔍 开始对比用例...")

            # 提取用例ID
            total_ids = self.extract_case_ids(total_df, '*TCID')
            done_ids = self.extract_case_ids(done_df, '用例ID')

            print(f"📈 全量用例数: {len(total_ids)}")
            print(f"📋 已完成用例数: {len(done_ids)}")

            # 找出未完成的用例ID
            missing_ids = total_ids - done_ids
            print(f"🔍 未完成用例数: {len(missing_ids)}")

            if len(missing_ids) == 0:
                print("🎉 所有用例都已完成！")
                return pd.DataFrame()

            # 筛选出未完成的用例记录
            missing_df = total_df[total_df['*TCID'].isin(missing_ids)].copy()

            # 添加对比时间戳
            missing_df['对比时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            missing_df['状态'] = '未完成'

            print(f"✅ 成功筛选出 {len(missing_df)} 条未完成用例记录")

            return missing_df

        except Exception as e:
            print(f"❌ 对比用例失败: {str(e)}")
            print(traceback.format_exc())
            return pd.DataFrame()

    def find_extra_cases(self, total_df, done_df):
        """
        找出已完成但不在全量用例中的用例（反向对比）
        :param total_df: 全量用例DataFrame
        :param done_df: 已完成用例DataFrame
        :return: 多余用例DataFrame
        """
        try:
            print("\n🔍 开始反向对比用例...")

            # 提取用例ID
            total_ids = self.extract_case_ids(total_df, '*TCID')
            done_ids = self.extract_case_ids(done_df, '用例ID')

            print(f"📈 全量用例数: {len(total_ids)}")
            print(f"📋 已完成用例数: {len(done_ids)}")

            # 找出多余的用例ID（在done中但不在total中）
            extra_ids = done_ids - total_ids
            print(f"🔍 多余用例数: {len(extra_ids)}")

            if len(extra_ids) == 0:
                print("🎉 没有多余的用例！")
                return pd.DataFrame()

            # 筛选出多余的用例记录
            extra_df = done_df[done_df['用例ID'].isin(extra_ids)].copy()

            # 添加对比时间戳
            extra_df['对比时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            extra_df['状态'] = '多余用例'

            print(f"✅ 成功筛选出 {len(extra_df)} 条多余用例记录")

            return extra_df

        except Exception as e:
            print(f"❌ 反向对比用例失败: {str(e)}")
            print(traceback.format_exc())
            return pd.DataFrame()
    
    def save_missing_cases(self, missing_df, output_path=None):
        """
        保存未完成用例到Excel文件
        :param missing_df: 未完成用例DataFrame
        :param output_path: 输出文件路径
        :return: 保存的文件路径
        """
        try:
            if missing_df.empty:
                print("📝 没有未完成的用例，无需保存文件")
                return None

            # 生成输出文件路径
            if output_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_path = f"task_manager/backend/src/data/excel/missing_cases_{timestamp}.xlsx"

            print(f"💾 正在保存未完成用例到: {os.path.basename(output_path)}")

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 保存到Excel文件
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 主要数据
                missing_df.to_excel(writer, sheet_name='未完成用例', index=False)

                # 统计信息
                stats_data = {
                    '统计项': ['全量用例数', '已完成用例数', '未完成用例数', '完成率'],
                    '数值': [
                        len(missing_df) + len(self.done_ids) if hasattr(self, 'done_ids') else '未知',
                        len(self.done_ids) if hasattr(self, 'done_ids') else '未知',
                        len(missing_df),
                        f"{(len(self.done_ids) / (len(missing_df) + len(self.done_ids)) * 100):.1f}%" if hasattr(self, 'done_ids') else '未知'
                    ]
                }
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)

                # 按模块分组统计
                if '*Component' in missing_df.columns:
                    component_stats = missing_df['*Component'].value_counts().reset_index()
                    component_stats.columns = ['模块', '未完成用例数']
                    component_stats.to_excel(writer, sheet_name='模块统计', index=False)

            print(f"✅ 文件保存成功: {output_path}")
            print(f"📊 保存了 {len(missing_df)} 条未完成用例记录")

            return output_path

        except Exception as e:
            print(f"❌ 保存文件失败: {str(e)}")
            print(traceback.format_exc())
            return None

    def save_extra_cases(self, extra_df, output_path=None):
        """
        保存多余用例到Excel文件
        :param extra_df: 多余用例DataFrame
        :param output_path: 输出文件路径
        :return: 保存的文件路径
        """
        try:
            if extra_df.empty:
                print("📝 没有多余的用例，无需保存文件")
                return None

            # 生成输出文件路径
            if output_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_path = f"task_manager/backend/src/data/excel/extra_cases_{timestamp}.xlsx"

            print(f"💾 正在保存多余用例到: {os.path.basename(output_path)}")

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 保存到Excel文件
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 主要数据
                extra_df.to_excel(writer, sheet_name='多余用例', index=False)

                # 统计信息
                stats_data = {
                    '统计项': ['全量用例数', '已完成用例数', '多余用例数'],
                    '数值': [
                        len(self.total_ids) if hasattr(self, 'total_ids') else '未知',
                        len(self.done_ids) if hasattr(self, 'done_ids') else '未知',
                        len(extra_df)
                    ]
                }
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)

                # 按模块分组统计
                if '模块名' in extra_df.columns:
                    module_stats = extra_df['模块名'].value_counts().reset_index()
                    module_stats.columns = ['模块', '多余用例数']
                    module_stats.to_excel(writer, sheet_name='模块统计', index=False)

            print(f"✅ 文件保存成功: {output_path}")
            print(f"📊 保存了 {len(extra_df)} 条多余用例记录")

            return output_path

        except Exception as e:
            print(f"❌ 保存文件失败: {str(e)}")
            print(traceback.format_exc())
            return None
    
    def run_comparison(self, output_path=None):
        """
        执行完整的用例对比流程
        :param output_path: 输出文件路径
        :return: 输出文件路径
        """
        print("🚀 开始用例对比分析...")
        print("=" * 60)

        # 读取全量用例文件
        total_df = self.read_excel_file(self.case_total_file, "全量用例文件")
        if total_df is None:
            return None

        # 读取已完成用例文件
        done_df = self.read_excel_file(self.case_done_file, "已完成用例文件")
        if done_df is None:
            return None

        # 提取用例ID用于统计
        total_ids = self.extract_case_ids(total_df, '*TCID')
        done_ids = self.extract_case_ids(done_df, '用例ID')
        self.done_ids = done_ids  # 保存供其他方法使用

        # 找出未完成的用例
        missing_df = self.find_missing_cases(total_df, done_df)

        # 生成分析报告
        self.generate_summary_report(missing_df, len(total_ids), len(done_ids))

        # 保存结果
        result_path = self.save_missing_cases(missing_df, output_path)

        print("\n" + "=" * 60)
        print("🎯 用例对比分析完成！")

        return result_path

    def run_reverse_comparison(self, output_path=None):
        """
        执行反向用例对比流程（找出已完成但不在全量用例中的用例）
        :param output_path: 输出文件路径
        :return: 输出文件路径
        """
        print("🚀 开始反向用例对比分析...")
        print("=" * 60)

        # 读取全量用例文件
        total_df = self.read_excel_file(self.case_total_file, "全量用例文件")
        if total_df is None:
            return None

        # 读取已完成用例文件
        done_df = self.read_excel_file(self.case_done_file, "已完成用例文件")
        if done_df is None:
            return None

        # 提取用例ID用于统计
        total_ids = self.extract_case_ids(total_df, '*TCID')
        done_ids = self.extract_case_ids(done_df, '用例ID')
        self.total_ids = total_ids  # 保存供其他方法使用
        self.done_ids = done_ids

        # 找出多余的用例
        extra_df = self.find_extra_cases(total_df, done_df)

        # 生成分析报告
        self.generate_reverse_summary_report(extra_df, len(total_ids), len(done_ids))

        # 保存结果
        result_path = self.save_extra_cases(extra_df, output_path)

        print("\n" + "=" * 60)
        print("🎯 反向用例对比分析完成！")

        return result_path

    def run_full_comparison(self, missing_output_path=None, extra_output_path=None):
        """
        执行完整的双向用例对比流程
        :param missing_output_path: 未完成用例输出文件路径
        :param extra_output_path: 多余用例输出文件路径
        :return: (未完成用例文件路径, 多余用例文件路径)
        """
        print("🚀 开始完整双向用例对比分析...")
        print("=" * 60)

        # 读取全量用例文件
        total_df = self.read_excel_file(self.case_total_file, "全量用例文件")
        if total_df is None:
            return None, None

        # 读取已完成用例文件
        done_df = self.read_excel_file(self.case_done_file, "已完成用例文件")
        if done_df is None:
            return None, None

        # 提取用例ID用于统计
        total_ids = self.extract_case_ids(total_df, '*TCID')
        done_ids = self.extract_case_ids(done_df, '用例ID')
        self.total_ids = total_ids
        self.done_ids = done_ids

        # 找出未完成的用例
        missing_df = self.find_missing_cases(total_df, done_df)

        # 找出多余的用例
        extra_df = self.find_extra_cases(total_df, done_df)

        # 生成综合分析报告
        self.generate_full_summary_report(missing_df, extra_df, len(total_ids), len(done_ids))

        # 保存结果
        missing_path = self.save_missing_cases(missing_df, missing_output_path)
        extra_path = self.save_extra_cases(extra_df, extra_output_path)

        print("\n" + "=" * 60)
        print("🎯 完整双向用例对比分析完成！")

        return missing_path, extra_path


    def generate_summary_report(self, missing_df, total_count, done_count):
        """
        生成详细的分析报告
        :param missing_df: 未完成用例DataFrame
        :param total_count: 全量用例数
        :param done_count: 已完成用例数
        """
        print("\n📊 分析报告:")
        print("-" * 40)
        print(f"📈 全量用例总数: {total_count}")
        print(f"✅ 已完成用例数: {done_count}")
        print(f"❌ 未完成用例数: {len(missing_df)}")
        print(f"📊 完成率: {(done_count / total_count * 100):.1f}%")

        if not missing_df.empty and '*Component' in missing_df.columns:
            print(f"\n🔍 未完成用例按模块分布:")
            component_stats = missing_df['*Component'].value_counts().head(5)
            for component, count in component_stats.items():
                print(f"   {component}: {count} 个用例")

        if not missing_df.empty and '*Owner' in missing_df.columns:
            print(f"\n👥 未完成用例按负责人分布:")
            owner_stats = missing_df['*Owner'].value_counts().head(5)
            for owner, count in owner_stats.items():
                print(f"   {owner}: {count} 个用例")

    def generate_reverse_summary_report(self, extra_df, total_count, done_count):
        """
        生成反向对比分析报告
        :param extra_df: 多余用例DataFrame
        :param total_count: 全量用例数
        :param done_count: 已完成用例数
        """
        print("\n📊 反向对比分析报告:")
        print("-" * 40)
        print(f"📈 全量用例总数: {total_count}")
        print(f"✅ 已完成用例数: {done_count}")
        print(f"⚠️ 多余用例数: {len(extra_df)}")

        if len(extra_df) > 0:
            print(f"📊 多余用例占比: {(len(extra_df) / done_count * 100):.1f}%")

        if not extra_df.empty and '模块名' in extra_df.columns:
            print(f"\n🔍 多余用例按模块分布:")
            module_stats = extra_df['模块名'].value_counts().head(5)
            for module, count in module_stats.items():
                print(f"   {module}: {count} 个用例")

        if not extra_df.empty and '脚本owner' in extra_df.columns:
            print(f"\n👥 多余用例按负责人分布:")
            owner_stats = extra_df['脚本owner'].value_counts().head(5)
            for owner, count in owner_stats.items():
                print(f"   {owner}: {count} 个用例")

    def generate_full_summary_report(self, missing_df, extra_df, total_count, done_count):
        """
        生成完整双向对比分析报告
        :param missing_df: 未完成用例DataFrame
        :param extra_df: 多余用例DataFrame
        :param total_count: 全量用例数
        :param done_count: 已完成用例数
        """
        print("\n📊 完整双向对比分析报告:")
        print("=" * 50)
        print(f"📈 全量用例总数: {total_count}")
        print(f"✅ 已完成用例数: {done_count}")
        print(f"❌ 未完成用例数: {len(missing_df)}")
        print(f"⚠️ 多余用例数: {len(extra_df)}")
        print(f"📊 完成率: {(done_count / total_count * 100):.1f}%")

        if len(extra_df) > 0:
            print(f"📊 多余用例占比: {(len(extra_df) / done_count * 100):.1f}%")

        print("\n📋 用例状态总结:")
        print(f"   ✅ 正确完成: {done_count - len(extra_df)} 个")
        print(f"   ❌ 待完成: {len(missing_df)} 个")
        print(f"   ⚠️ 多余: {len(extra_df)} 个")


def main():
    """主函数"""
    print("📋 用例对比工具 v2.0")
    print("=" * 60)
    print("功能选择:")
    print("1. 正向对比 - 找出未完成的用例")
    print("2. 反向对比 - 找出多余的用例")
    print("3. 双向对比 - 同时进行正向和反向对比")
    print("=" * 60)

    # 创建对比工具实例
    tool = CaseComparisonTool()

    # 获取用户选择
    try:
        choice = input("请选择功能 (1/2/3，默认为3): ").strip()
        if not choice:
            choice = "3"
    except:
        choice = "3"

    if choice == "1":
        print("\n🚀 执行正向对比...")
        result_path = tool.run_comparison()
        if result_path:
            print(f"\n🎉 正向对比完成！结果已保存到:")
            print(f"📁 {result_path}")

    elif choice == "2":
        print("\n🚀 执行反向对比...")
        result_path = tool.run_reverse_comparison()
        if result_path:
            print(f"\n🎉 反向对比完成！结果已保存到:")
            print(f"📁 {result_path}")

    elif choice == "3":
        print("\n🚀 执行双向对比...")
        missing_path, extra_path = tool.run_full_comparison()
        if missing_path or extra_path:
            print(f"\n🎉 双向对比完成！结果已保存到:")
            if missing_path:
                print(f"📁 未完成用例: {missing_path}")
            if extra_path:
                print(f"📁 多余用例: {extra_path}")

    else:
        print("❌ 无效选择，请重新运行程序")


if __name__ == "__main__":
    main()
