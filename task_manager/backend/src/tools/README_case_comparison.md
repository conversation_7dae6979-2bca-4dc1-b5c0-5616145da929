# 用例对比工具使用说明

## 功能概述

用例对比工具用于比较全量用例和已完成用例，自动生成未完成用例清单。

## 输入文件

1. **case_total.xlsx** - 全量用例文件
   - 路径: `task_manager/backend/src/data/excel/case_total.xlsx`
   - 关键列: `*TCID` (用例ID)
   - 包含所有需要完成的用例

2. **case_done.xlsx** - 已完成用例文件
   - 路径: `task_manager/backend/src/data/excel/case_done.xlsx`
   - 关键列: `用例ID`
   - 包含已经完成的用例

## 输出文件

生成的Excel文件包含3个工作表：

### 1. 未完成用例
- 详细的未完成用例列表
- 包含原始数据的所有列
- 新增字段：
  - `对比时间`: 分析执行时间
  - `状态`: 标记为"未完成"

### 2. 统计信息
- 全量用例数
- 已完成用例数
- 未完成用例数
- 完成率

### 3. 模块统计
- 按模块分组的未完成用例统计
- 便于了解各模块的完成情况

## 使用方法

### 方法1: 直接运行完整工具
```bash
python case_comparison_tool.py
```

### 方法2: 运行简化版本
```bash
python run_case_comparison.py
```

### 方法3: 在代码中调用
```python
from case_comparison_tool import CaseComparisonTool

# 创建工具实例
tool = CaseComparisonTool()

# 执行对比分析
result_path = tool.run_comparison()

# 自定义输出路径
result_path = tool.run_comparison("custom_output.xlsx")
```

## 输出示例

```
📋 用例对比工具
============================================================
功能: 比较全量用例和已完成用例，生成未完成用例清单
输入: case_total.xlsx (全量用例) + case_done.xlsx (已完成用例)
输出: missing_cases_[时间戳].xlsx (未完成用例)
============================================================

📊 分析报告:
----------------------------------------
📈 全量用例总数: 226
✅ 已完成用例数: 65
❌ 未完成用例数: 238
📊 完成率: 28.8%

🔍 未完成用例按模块分布:
   TransID: 87 个用例
   Ella语音: 81 个用例
   SmartMessage: 69 个用例
   MOL: 1 个用例

🎉 分析完成！结果已保存到:
📁 missing_cases_20250822_172541.xlsx
```

## 技术特性

- ✅ 自动检测Excel文件格式
- ✅ 智能列名识别
- ✅ 数据清洗和验证
- ✅ 多工作表输出
- ✅ 详细的统计分析
- ✅ 错误处理和日志记录
- ✅ 支持大文件处理

## 依赖要求

```python
pandas
openpyxl
xlrd
```

## 注意事项

1. 确保输入文件路径正确
2. 输入文件必须是有效的Excel格式
3. 用例ID列必须存在且不为空
4. 输出目录会自动创建

## 故障排除

### 常见问题

1. **文件不存在错误**
   - 检查文件路径是否正确
   - 确认文件确实存在

2. **列名不匹配错误**
   - 检查Excel文件中的列名
   - 确认`*TCID`和`用例ID`列存在

3. **权限错误**
   - 确保有读取输入文件的权限
   - 确保有写入输出目录的权限

4. **内存不足**
   - 对于大文件，确保有足够的内存
   - 考虑分批处理

## 更新日志

- v1.0: 基础功能实现
- v1.1: 添加详细统计报告
- v1.2: 优化用户界面和错误处理
