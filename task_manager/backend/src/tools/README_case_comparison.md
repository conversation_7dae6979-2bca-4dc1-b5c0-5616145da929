# 用例对比工具使用说明 v2.0

## 功能概述

用例对比工具v2.0支持三种对比模式：
1. **正向对比** - 找出未完成的用例（在全量用例中但不在已完成用例中）
2. **反向对比** - 找出多余的用例（在已完成用例中但不在全量用例中）
3. **双向对比** - 同时进行正向和反向对比，提供完整的用例状态分析

## 输入文件

1. **case_total.xlsx** - 全量用例文件
   - 路径: `task_manager/backend/src/data/excel/case_total.xlsx`
   - 关键列: `*TCID` (用例ID)
   - 包含所有需要完成的用例

2. **case_done.xlsx** - 已完成用例文件
   - 路径: `task_manager/backend/src/data/excel/case_done.xlsx`
   - 关键列: `用例ID`
   - 包含已经完成的用例

## 输出文件

根据对比模式不同，生成不同的Excel文件：

### 正向对比输出 (missing_cases_[时间戳].xlsx)
包含3个工作表：
1. **未完成用例** - 详细的未完成用例列表
2. **统计信息** - 全量用例数、已完成用例数、未完成用例数、完成率
3. **模块统计** - 按模块分组的未完成用例统计

### 反向对比输出 (extra_cases_[时间戳].xlsx)
包含3个工作表：
1. **多余用例** - 详细的多余用例列表
2. **统计信息** - 全量用例数、已完成用例数、多余用例数
3. **模块统计** - 按模块分组的多余用例统计

### 双向对比输出
同时生成两个文件：
- `missing_cases_[时间戳].xlsx` - 未完成用例
- `extra_cases_[时间戳].xlsx` - 多余用例

### 新增字段说明
所有输出文件都会添加以下字段：
- `对比时间`: 分析执行时间
- `状态`: "未完成" 或 "多余用例"

## 使用方法

### 方法1: 交互式运行完整工具
```bash
python case_comparison_tool.py
# 然后选择功能：
# 1 - 正向对比
# 2 - 反向对比
# 3 - 双向对比（默认）
```

### 方法2: 运行特定功能的简化版本
```bash
# 正向对比（找未完成用例）
python run_case_comparison.py

# 反向对比（找多余用例）
python run_reverse_comparison.py

# 双向对比（同时进行两种对比）
python run_full_comparison.py
```

### 方法3: 在代码中调用
```python
from case_comparison_tool import CaseComparisonTool

# 创建工具实例
tool = CaseComparisonTool()

# 正向对比 - 找未完成用例
missing_path = tool.run_comparison()

# 反向对比 - 找多余用例
extra_path = tool.run_reverse_comparison()

# 双向对比 - 同时进行两种对比
missing_path, extra_path = tool.run_full_comparison()

# 自定义输出路径
missing_path = tool.run_comparison("custom_missing.xlsx")
extra_path = tool.run_reverse_comparison("custom_extra.xlsx")
```

## 输出示例

### 双向对比输出示例
```
📋 用例对比工具 v2.0
============================================================
功能选择:
1. 正向对比 - 找出未完成的用例
2. 反向对比 - 找出多余的用例
3. 双向对比 - 同时进行正向和反向对比
============================================================

📊 完整双向对比分析报告:
==================================================
📈 全量用例总数: 303
✅ 已完成用例数: 65
❌ 未完成用例数: 281
⚠️ 多余用例数: 43
📊 完成率: 21.5%
📊 多余用例占比: 66.2%

📋 用例状态总结:
   ✅ 正确完成: 22 个
   ❌ 待完成: 281 个
   ⚠️ 多余: 43 个

🎉 双向对比完成！结果已保存到:
📁 未完成用例: missing_cases_20250822_174450.xlsx
📁 多余用例: extra_cases_20250822_174450.xlsx
```

### 反向对比输出示例
```
📊 反向对比分析报告:
----------------------------------------
📈 全量用例总数: 303
✅ 已完成用例数: 65
⚠️ 多余用例数: 43
📊 多余用例占比: 66.2%

🔍 多余用例按模块分布:
   AI_Gallery: 33 个用例
   ELLA: 10 个用例

👥 多余用例按负责人分布:
   @李松: 43 个用例
```

## 技术特性

- ✅ 自动检测Excel文件格式
- ✅ 智能列名识别
- ✅ 数据清洗和验证
- ✅ 多工作表输出
- ✅ 详细的统计分析
- ✅ 错误处理和日志记录
- ✅ 支持大文件处理

## 依赖要求

```python
pandas
openpyxl
xlrd
```

## 注意事项

1. 确保输入文件路径正确
2. 输入文件必须是有效的Excel格式
3. 用例ID列必须存在且不为空
4. 输出目录会自动创建

## 故障排除

### 常见问题

1. **文件不存在错误**
   - 检查文件路径是否正确
   - 确认文件确实存在

2. **列名不匹配错误**
   - 检查Excel文件中的列名
   - 确认`*TCID`和`用例ID`列存在

3. **权限错误**
   - 确保有读取输入文件的权限
   - 确保有写入输出目录的权限

4. **内存不足**
   - 对于大文件，确保有足够的内存
   - 考虑分批处理

## 更新日志

- v1.0: 基础功能实现（正向对比）
- v1.1: 添加详细统计报告
- v1.2: 优化用户界面和错误处理
- v2.0: 新增反向对比和双向对比功能
  - ✅ 反向对比：找出已完成但不在全量用例中的用例
  - ✅ 双向对比：同时进行正向和反向对比
  - ✅ 交互式功能选择界面
  - ✅ 专用的简化运行脚本
  - ✅ 完善的分析报告和统计信息
