#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用例对比工具 - 简化运行脚本
快速运行用例对比分析
"""

import os
import sys
from case_comparison_tool import CaseComparisonTool


def main():
    """主函数 - 简化版本"""
    print("🚀 快速用例对比分析")
    print("=" * 50)
    
    # 创建工具实例
    tool = CaseComparisonTool()
    
    # 检查输入文件是否存在
    if not os.path.exists(tool.case_total_file):
        print(f"❌ 全量用例文件不存在: {tool.case_total_file}")
        return False
    
    if not os.path.exists(tool.case_done_file):
        print(f"❌ 已完成用例文件不存在: {tool.case_done_file}")
        return False
    
    print("✅ 输入文件检查通过")
    
    # 执行对比分析
    result_path = tool.run_comparison()
    
    if result_path:
        print(f"\n🎉 成功！结果文件: {os.path.basename(result_path)}")
        return True
    else:
        print("\n❌ 分析失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
