#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反向用例对比工具 - 简化运行脚本
找出已完成但不在全量用例中的用例
"""

import os
import sys
from case_comparison_tool import CaseComparisonTool


def main():
    """主函数 - 反向对比版本"""
    print("🔄 反向用例对比分析")
    print("=" * 50)
    print("功能: 找出已完成但不在全量用例中的用例")
    print("输入: case_total.xlsx + case_done.xlsx")
    print("输出: extra_cases_[时间戳].xlsx")
    print("=" * 50)
    
    # 创建工具实例
    tool = CaseComparisonTool()
    
    # 检查输入文件是否存在
    if not os.path.exists(tool.case_total_file):
        print(f"❌ 全量用例文件不存在: {tool.case_total_file}")
        return False
    
    if not os.path.exists(tool.case_done_file):
        print(f"❌ 已完成用例文件不存在: {tool.case_done_file}")
        return False
    
    print("✅ 输入文件检查通过")
    
    # 执行反向对比分析
    result_path = tool.run_reverse_comparison()
    
    if result_path:
        print(f"\n🎉 成功！多余用例文件: {os.path.basename(result_path)}")
        print(f"\n💡 提示: Excel文件包含3个工作表:")
        print(f"   📋 多余用例 - 详细的多余用例列表")
        print(f"   📊 统计信息 - 整体统计")
        print(f"   🏢 模块统计 - 按模块分组的统计信息")
        return True
    else:
        print("\n❌ 分析失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
