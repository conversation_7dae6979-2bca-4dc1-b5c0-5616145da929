import json

def preprocess_data_by_dept_third(data):
    """
    按照三级部门分组数据进行预处理
    
    参数:
        data (dict): 原始数据，包含data字段的字典
        
    返回:
        dict: 按三级部门分组的数据，deptNameThird为空的放在leader中
        
    示例:
        {
            'RT_系统产品测试部': [
                {'workerName': '杨将来', 'noneSubmissionDays': 2},
                {'workerName': '卜昌义', 'noneSubmissionDays': 2}
            ],
            'RT_独立产品测试部': [
                {'workerName': '李志祥', 'noneSubmissionDays': 2},
                {'workerName': '卜昌义', 'noneSubmissionDays': 2}
            ],
            'leader': [
                {'workerName': '卜昌义', 'noneSubmissionDays': 2}
            ]
        }
    """
    result = {}
    
    # 检查数据格式
    if not isinstance(data, dict) or 'data' not in data:
        raise ValueError("输入数据格式错误，需要包含'data'字段的字典")
    
    data_list = data.get('data', [])
    
    for item in data_list:
        # 提取需要的字段
        worker_name = item.get('workerName', '')
        none_submission_days = item.get('noneSubmissionDays', 0)
        dept_name_third = item.get('deptNameThird', '')
        
        # 构建输出项
        worker_info = {
            'workerName': worker_name,
            'noneSubmissionDays': none_submission_days
        }
        
        # 根据deptNameThird是否为空进行分组
        if dept_name_third and dept_name_third.strip():
            # deptNameThird不为空，按部门分组
            if dept_name_third not in result:
                result[dept_name_third] = []
            result[dept_name_third].append(worker_info)
        else:
            # deptNameThird为空，放入leader组
            if 'leader' not in result:
                result['leader'] = []
            result['leader'].append(worker_info)
    
    return result

if __name__ == '__main__':
    # 测试预处理函数
    test_data = {
        "current": 1,
        "total": 46,
        "size": 100,
        "pages": 1,
        "data": [
            {
                "workerName": "杨将来",
                "worker": "18652380",
                "workerType": "编内",
                "workerOnOffJob": "在职",
                "noneSubmissionDays": 2,
                "dateList": [
                    "2025-08-06 00:00:00",
                    "2025-08-07 00:00:00"
                ],
                "date": None,
                "dept": None,
                "deptNo": None,
                "deptNameFirst": "研发测试部",
                "deptNameSecond": "RT_软件产品测试部",
                "deptNameThird": "RT_系统产品测试部",
                "level": None,
                "location": "中国_重庆",
                "no": None
            },
            {
                "workerName": "李志祥",
                "worker": "18652381",
                "workerType": "编内",
                "workerOnOffJob": "在职",
                "noneSubmissionDays": 2,
                "dateList": [
                    "2025-08-06 00:00:00",
                    "2025-08-07 00:00:00"
                ],
                "date": None,
                "dept": None,
                "deptNo": None,
                "deptNameFirst": "研发测试部",
                "deptNameSecond": "RT_软件产品测试部",
                "deptNameThird": "RT_独立产品测试部",
                "level": None,
                "location": "中国_重庆",
                "no": None
            },
            {
                "workerName": "卜昌义",
                "worker": "18652382",
                "workerType": "编内",
                "workerOnOffJob": "在职",
                "noneSubmissionDays": 2,
                "dateList": [
                    "2025-08-06 00:00:00",
                    "2025-08-07 00:00:00"
                ],
                "date": None,
                "dept": None,
                "deptNo": None,
                "deptNameFirst": "研发测试部",
                "deptNameSecond": "RT_软件产品测试部",
                "deptNameThird": "",  # 空字符串，应该放入leader
                "level": None,
                "location": "中国_重庆",
                "no": None
            },
            {
                "workerName": "张三",
                "worker": "18652383",
                "workerType": "编内",
                "workerOnOffJob": "在职",
                "noneSubmissionDays": 3,
                "dateList": [
                    "2025-08-05 00:00:00",
                    "2025-08-06 00:00:00",
                    "2025-08-07 00:00:00"
                ],
                "date": None,
                "dept": None,
                "deptNo": None,
                "deptNameFirst": "研发测试部",
                "deptNameSecond": "RT_软件产品测试部",
                "deptNameThird": "RT_系统产品测试部",  # 与杨将来同部门
                "level": None,
                "location": "中国_重庆",
                "no": None
            },
            {
                "workerName": "李四",
                "worker": "18652384",
                "workerType": "编内",
                "workerOnOffJob": "在职",
                "noneSubmissionDays": 1,
                "dateList": [
                    "2025-08-07 00:00:00"
                ],
                "date": None,
                "dept": None,
                "deptNo": None,
                "deptNameFirst": "研发测试部",
                "deptNameSecond": "RT_软件产品测试部",
                "deptNameThird": None,  # None值，应该放入leader
                "level": None,
                "location": "中国_重庆",
                "no": None
            }
        ]
    }
    
    # 测试预处理函数
    result = preprocess_data_by_dept_third(test_data)
    print("预处理结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
